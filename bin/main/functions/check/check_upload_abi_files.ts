import { spawnSync } from 'child_process'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message, SRC_ZONE_ID } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * 対象環境にてABIファイルが正しく設定されているか確認する
 * @param network 環境名
 */
export async function checkUploadABIfiles(network: string) {
  try {
    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile, SRC_ZONE_ID)

    const zoneId = process.env.ZONE_ID
    const backupS3 = process.env.BACKUP_S3
    if (zoneId == null || backupS3 == null) {
      message('err', 'environment variable is not set.')
      process.exit(1)
    }

    // メインコントラクト Fin/Bizで確認するファイルのリストを定義
    const mainContractFiles =
      zoneId === '3000'
        ? [
            'Account.json',
            'BusinessZoneAccount.json',
            'FinancialCheck.json',
            'FinancialZoneAccount.json',
            'IBCToken.json',
            'Issuer.json',
            'Provider.json',
            'Token.json',
            'Validator.json',
          ]
        : [
            'Account.json',
            'FinancialCheck.json',
            'IBCToken.json',
            'Issuer.json',
            'Provider.json',
            'Token.json',
            'Validator.json',
          ]

    // IBCコントラクト 確認するファイルの定義
    const ibcContractFiles = ['AccountSyncBridge.json', 'BalanceSyncBridge.json', 'JPYTokenTransferBridge.json']

    // 実行時の日付取得
    const today = new Date().toISOString().slice(0, 10) // YYYY-MM-DD

    // メインコントラクトの各ファイルチェック
    console.log('Contract のリリース時のabiファイルが正しくアップロードされたか確認します。')
    checkFileExists(backupS3, zoneId, mainContractFiles, today)
    console.log('Contract 確認終了しました。')

    // IBCコントラクトの各ファイルチェック
    console.log('Contract-Ibc のリリース時のabiファイルが正しくアップロードされたか確認します。')
    checkFileExists(backupS3, zoneId, ibcContractFiles, today)
    console.log('Contract-Ibc 確認終了しました。')
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

/**
 * 指定したS3パスの対象ゾーンに指定したファイル群が存在するか、またアップロード日付が本日であるか判定しコンソールに表示する
 * @param backupPass s3バックアップパス
 * @param zoneID ゾーンID
 * @param files ファイル群
 * @param today 本日の日付
 */
function checkFileExists(backupPass: string, zoneID: string, files: string[], today: string) {
  for (const fileName of files) {
    const s3Path = `s3://${backupPass}/${zoneID}/${fileName}`
    const lsResult = spawnSync('aws', ['s3', 'ls', s3Path], { encoding: 'utf-8' })
    const output = lsResult.stdout?.toString().trim()

    if (!output) {
      message('e', `NG：${fileName} がアップロードされていません。`)
      continue
    }

    const uploadDate = output.split(/\s+/)[0] // 先頭の日付
    if (uploadDate === today) {
      message('s', `OK：${fileName} のアップロード日付が本日であることを確認しました。`)
    } else {
      message('e', `NG：${fileName} のアップロード日付が本日ではありません。`)
    }
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/check/${scriptName} [NETWORK name]`)
    process.exit(1)
  }

  checkUploadABIfiles(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
