import fs from 'fs'
import path from 'path'
import dotenv from 'dotenv'
import {
  message,
  ROOTDIR,
  IBCAPP_TOKEN_TRANSFER,
  IBCAPP_ACCOUNT_SYNC,
  IBCAPP_BALANCE_SYNC,
} from '../../../common/utils'
import { getExtractBridgeAddress } from '../helpers/get_extract_bridge_address'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * リリース後にメインコントラクト、IBCコントラクトに双方で生成したアドレスが設定されているか確認する
 * @param dir メインコントラクトで生成されたabiファイル格納path（例：/Users/<USER>/work/dcbg-dcjpy-contract/deployments/mainFin）
 */
export async function checkSetAddress(dir: string) {
  try {
    // 環境変読み出し
    dotenv.config({ path: path.join(ROOTDIR, '.env') })
    const network = process.env.NETWORK

    if (network == null) {
      message('err', 'NETWORK environment variable is not set.')
      process.exit(1)
    }

    // メインコントラクトのアドレス取得
    const SET_ADDR_TOKEN_TRANSFER = getExtractBridgeAddress(network, IBCAPP_TOKEN_TRANSFER)
    const SET_ADDR_ACCOUNT_SYNC = getExtractBridgeAddress(network, IBCAPP_ACCOUNT_SYNC)
    const SET_ADDR_BALANCE_SYNC = getExtractBridgeAddress(network, IBCAPP_BALANCE_SYNC)

    // IBCコントラクトのアドレス取得
    const deployDir = path.join('deployments', network)
    const SET_IBC_ADDR_TOKEN_TRANSFER = loadAddress(deployDir, 'JPYTokenTransferBridge.json')
    const SET_IBC_ADDR_ACCOUNT_SYNC = loadAddress(deployDir, 'AccountSyncBridge.json')
    const SET_IBC_ADDR_BALANCE_SYNC = loadAddress(deployDir, 'BalanceSyncBridge.json')

    // IBCコントラクトのアドレス取得
    const s3restoreDir = path.join('s3-restore', 'deployments', network)
    const SET_IBC_TOKEN_ADDRESS = loadAddress(s3restoreDir, 'IBCToken.json')
    const SET_VALIDATOR_ADDRESS = loadAddress(s3restoreDir, 'Validator.json')
    const SET_ACCOUNT_ADDRESS = loadAddress(s3restoreDir, 'Account.json')
    const SET_ACCESS_CTRL_ADDRESS = loadAddress(s3restoreDir, 'AccessCtrl.json')

    // メインコントラクトのアドレス取得
    const SET_MAIN_IBC_TOKEN_ADDRESS = loadAddress(dir, 'IBCToken.json')
    const SET_MAIN_VALIDATOR_ADDRESS = loadAddress(dir, 'Validator.json')
    const SET_MAIN_ACCOUNT_ADDRESS = loadAddress(dir, 'Account.json')
    const SET_MAIN_ACCESS_CTRL_ADDRESS = loadAddress(dir, 'AccessCtrl.json')

    console.log('メインコントラクトにIBCコントラクトで生成されたアドレスが設定されたことを確認します。')
    check('JPYTokenTransferBridge.json', SET_ADDR_TOKEN_TRANSFER, SET_IBC_ADDR_TOKEN_TRANSFER, false)
    check('AccountSyncBridge.json', SET_ADDR_ACCOUNT_SYNC, SET_IBC_ADDR_ACCOUNT_SYNC, false)
    check('BalanceSyncBridge.json', SET_ADDR_BALANCE_SYNC, SET_IBC_ADDR_BALANCE_SYNC, false)
    console.log('メインコントラクトのアドレスの確認が完了しました。')

    console.log('IBCコントラクトにメインコントラクトで生成されたアドレスが設定されたことを確認します。')
    check('IBCToken.json', SET_IBC_TOKEN_ADDRESS, SET_MAIN_IBC_TOKEN_ADDRESS, true)
    check('Validator.json', SET_VALIDATOR_ADDRESS, SET_MAIN_VALIDATOR_ADDRESS, true)
    check('Account.json', SET_ACCOUNT_ADDRESS, SET_MAIN_ACCOUNT_ADDRESS, true)
    check('AccessCtrl.json', SET_ACCESS_CTRL_ADDRESS, SET_MAIN_ACCESS_CTRL_ADDRESS, true)
    console.log('IBCコントラクトのアドレスの確認が完了しました。')
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

/**
 * 指定したJSONファイルからアドレス部を取り出す
 * @param filePath ファイルパス
 * @param jsonName JSON名
 * @returns アドレス
 */
function loadAddress(filePath: string, jsonName: string): string {
  const targetJsonPath = path.join(filePath, jsonName)
  const json = JSON.parse(fs.readFileSync(targetJsonPath, 'utf-8'))
  return json.address
}

/**
 * 指定したアドレスを比較して、結果をコンソールに表示する
 * @param name JSON名
 * @param main メインコントラクトのアドレス
 * @param ibc IBCコントラクトのアドレス
 * @param fromIBC 生成元がIBCコントラクトであるかどうか
 */
function check(name: string, main: string, ibc: string, fromIBC: boolean) {
  let from = 'メイン'
  let to = 'IBC'

  if (fromIBC) {
    from = 'IBC'
    to = 'メイン'
  }
  if (main === ibc) {
    message(
      's',
      `OK：${from}コントラクトに設定した${name} のアドレスが${to}コントラクトで生成されたアドレスであることを確認しました。`,
    )
  } else {
    message(
      'e',
      `NG：${from}コントラクトに設定した${name} のアドレスが${to}コントラクトで生成されたアドレスではありません`,
    )
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const filePath = process.argv[2]
  if (!filePath) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/check/${scriptName} [DEPLOYMENT_DIRECTORY name]`)
    process.exit(1)
  }

  checkSetAddress(filePath).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
